"""
报告导出模块
支持PDF和HTML格式的报告导出
"""

import os
import base64
from datetime import datetime
from typing import Dict, List, Optional
from io import BytesIO

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib.colors import HexColor
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.platypus.tableofcontents import TableOfContents
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR
)


class ReportExporter:
    """报告导出器"""
    
    def __init__(self):
        self.setup_fonts()
    
    def setup_fonts(self):
        """设置字体"""
        if REPORTLAB_AVAILABLE:
            try:
                # 尝试注册中文字体
                font_paths = [
                    'C:/Windows/Fonts/msyh.ttc',  # 微软雅黑
                    'C:/Windows/Fonts/simhei.ttf',  # 黑体
                    'C:/Windows/Fonts/simsun.ttc',  # 宋体
                ]
                
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                            break
                        except:
                            continue
            except Exception as e:
                print(f"字体注册失败: {e}")
    
    def export_to_pdf(self, report_content: Dict, file_path: str, charts: Optional[Dict] = None) -> bool:
        """导出PDF报告"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("reportlab库未安装，无法导出PDF。请运行: pip install reportlab")
        
        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(
                file_path,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # 创建样式
            styles = self.create_pdf_styles()
            
            # 构建文档内容
            story = []
            
            # 添加标题页
            self.add_title_page(story, report_content, styles)
            
            # 添加各个章节
            for section in report_content.get('sections', []):
                self.add_pdf_section(story, section, styles, charts)
            
            # 生成PDF
            doc.build(story)
            return True
            
        except Exception as e:
            raise Exception(f"PDF生成失败: {str(e)}")
    
    def create_pdf_styles(self) -> Dict:
        """创建PDF样式"""
        styles = getSampleStyleSheet()
        
        # 自定义样式
        custom_styles = {
            'Title': ParagraphStyle(
                'CustomTitle',
                parent=styles['Title'],
                fontSize=24,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=HexColor(ACCENT_COLOR),
                fontName='ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
            ),
            'Heading1': ParagraphStyle(
                'CustomHeading1',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=12,
                spaceBefore=20,
                textColor=HexColor(ACCENT_COLOR),
                fontName='ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
            ),
            'Heading2': ParagraphStyle(
                'CustomHeading2',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=8,
                spaceBefore=12,
                textColor=HexColor(TEXT_PRIMARY),
                fontName='ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
            ),
            'Normal': ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=6,
                textColor=HexColor(TEXT_PRIMARY),
                fontName='ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
            ),
            'Caption': ParagraphStyle(
                'CustomCaption',
                parent=styles['Normal'],
                fontSize=9,
                alignment=TA_CENTER,
                textColor=HexColor(TEXT_SECONDARY),
                spaceAfter=12,
                fontName='ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
            )
        }
        
        return custom_styles
    
    def add_title_page(self, story: List, report_content: Dict, styles: Dict):
        """添加标题页"""
        # 主标题
        title = Paragraph(report_content.get('title', '检测报告'), styles['Title'])
        story.append(title)
        story.append(Spacer(1, 0.5*inch))
        
        # 副标题
        if report_content.get('subtitle'):
            subtitle = Paragraph(report_content['subtitle'], styles['Heading2'])
            story.append(subtitle)
            story.append(Spacer(1, 0.3*inch))
        
        # 生成时间
        generated_time = Paragraph(
            f"生成时间: {report_content.get('generated_time', '')}",
            styles['Normal']
        )
        story.append(generated_time)
        story.append(Spacer(1, 1*inch))
        
        # 分页
        from reportlab.platypus import PageBreak
        story.append(PageBreak())
    
    def add_pdf_section(self, story: List, section: Dict, styles: Dict, charts: Optional[Dict] = None):
        """添加PDF章节"""
        # 章节标题
        title = Paragraph(section.get('title', ''), styles['Heading1'])
        story.append(title)
        
        section_type = section.get('type', '')
        content = section.get('content', {})
        
        if section_type in ['info', 'summary', 'comparison']:
            self.add_pdf_table(story, content, styles)
        elif section_type == 'diagnosis':
            self.add_pdf_diagnosis_table(story, content, styles)
        elif section_type == 'file_list':
            self.add_pdf_file_list_table(story, content, styles)
        elif section_type == 'features':
            self.add_pdf_features_content(story, content, styles)
        
        # 添加图表（如果有）
        if charts and section_type in charts:
            chart_data = charts[section_type]
            if chart_data:
                self.add_pdf_chart(story, chart_data, styles)
        
        story.append(Spacer(1, 0.3*inch))
    
    def add_pdf_table(self, story: List, content: Dict, styles: Dict):
        """添加PDF表格"""
        if not content:
            return
        
        # 准备表格数据
        data = [['项目', '值']]
        for key, value in content.items():
            data.append([str(key), str(value)])
        
        # 创建表格
        table = Table(data, colWidths=[3*inch, 4*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor(ACCENT_COLOR)),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTNAME', (0, 1), (-1, -1), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F9F9F9')),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#CCCCCC'))
        ]))
        
        story.append(table)
        story.append(Spacer(1, 0.2*inch))
    
    def add_pdf_diagnosis_table(self, story: List, content: List[Dict], styles: Dict):
        """添加PDF诊断表格"""
        if not content:
            return
        
        # 准备表格数据
        headers = ['诊断时间', '算法类型', '算法名称', '故障类型', '置信度', '状态']
        data = [headers]
        
        for result in content:
            row = [
                result.get('诊断时间', ''),
                result.get('算法类型', ''),
                result.get('算法名称', ''),
                result.get('故障类型', ''),
                result.get('置信度', ''),
                result.get('状态', '')
            ]
            data.append(row)
        
        # 创建表格
        table = Table(data, colWidths=[1.2*inch, 1*inch, 1.2*inch, 1*inch, 0.8*inch, 0.8*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor(ACCENT_COLOR)),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTNAME', (0, 1), (-1, -1), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F9F9F9')),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#CCCCCC')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(table)
        story.append(Spacer(1, 0.2*inch))
    
    def add_pdf_file_list_table(self, story: List, content: List[Dict], styles: Dict):
        """添加PDF文件列表表格"""
        if not content:
            return
        
        # 准备表格数据
        headers = ['序号', '文件名', '车型', '部件', '测试时间']
        data = [headers]
        
        for file_info in content:
            row = [
                str(file_info.get('序号', '')),
                file_info.get('文件名', ''),
                file_info.get('车型', ''),
                file_info.get('部件', ''),
                file_info.get('测试时间', '')
            ]
            data.append(row)
        
        # 创建表格
        table = Table(data, colWidths=[0.5*inch, 2*inch, 1.5*inch, 1.5*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor(ACCENT_COLOR)),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTNAME', (0, 1), (-1, -1), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F9F9F9')),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#CCCCCC')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(table)
        story.append(Spacer(1, 0.2*inch))
    
    def add_pdf_features_content(self, story: List, content: Dict, styles: Dict):
        """添加PDF特征内容"""
        for feature_type, features in content.items():
            # 特征类型标题
            type_title = Paragraph(feature_type, styles['Heading2'])
            story.append(type_title)
            
            # 特征表格
            headers = ['特征名称', '特征值', '提取时间']
            data = [headers]
            
            for feature in features:
                row = [
                    feature.get('特征名称', ''),
                    str(feature.get('特征值', '')),
                    feature.get('提取时间', '')
                ]
                data.append(row)
            
            table = Table(data, colWidths=[2*inch, 2*inch, 2*inch])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), HexColor(SECONDARY_BG)),
                ('TEXTCOLOR', (0, 0), (-1, 0), HexColor(TEXT_PRIMARY)),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTNAME', (0, 1), (-1, -1), 'ChineseFont' if 'ChineseFont' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F9F9F9')),
                ('GRID', (0, 0), (-1, -1), 1, HexColor('#CCCCCC')),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
            ]))
            
            story.append(table)
            story.append(Spacer(1, 0.15*inch))
    
    def add_pdf_chart(self, story: List, chart_base64: str, styles: Dict):
        """添加PDF图表"""
        try:
            # 解码base64图片
            image_data = base64.b64decode(chart_base64)
            image_buffer = BytesIO(image_data)
            
            # 创建图片对象
            img = Image(image_buffer, width=6*inch, height=4*inch)
            story.append(img)
            story.append(Spacer(1, 0.1*inch))
            
        except Exception as e:
            # 如果图片添加失败，添加错误信息
            error_text = Paragraph(f"图表加载失败: {str(e)}", styles['Normal'])
            story.append(error_text)
    
    def export_to_html(self, report_content: Dict, file_path: str, charts: Optional[Dict] = None) -> bool:
        """导出HTML报告"""
        try:
            html_content = self.generate_html_content(report_content, charts)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return True
            
        except Exception as e:
            raise Exception(f"HTML生成失败: {str(e)}")
    
    def generate_html_content(self, report_content: Dict, charts: Optional[Dict] = None) -> str:
        """生成HTML内容"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{report_content.get('title', '检测报告')}</title>
            <style>
                {self.get_html_styles()}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="title">{report_content.get('title', '检测报告')}</div>
                    <div class="subtitle">{report_content.get('subtitle', '')}</div>
                    <div class="generated-time">生成时间: {report_content.get('generated_time', '')}</div>
                </div>
        """
        
        # 添加各个章节
        for section in report_content.get('sections', []):
            html += self.generate_html_section(section, charts)
        
        html += """
            </div>
        </body>
        </html>
        """
        
        return html
    
    def get_html_styles(self) -> str:
        """获取HTML样式"""
        return f"""
            body {{
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                line-height: 1.6;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid {ACCENT_COLOR};
                padding-bottom: 20px;
            }}
            .title {{
                font-size: 28px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
            }}
            .subtitle {{
                font-size: 16px;
                color: {TEXT_SECONDARY};
                margin-bottom: 5px;
            }}
            .generated-time {{
                font-size: 12px;
                color: {TEXT_SECONDARY};
            }}
            .section {{
                margin-bottom: 30px;
                padding: 20px;
                background-color: {SECONDARY_BG};
                border-radius: 8px;
                border-left: 4px solid {ACCENT_COLOR};
            }}
            .section-title {{
                font-size: 20px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 15px;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
                background-color: white;
            }}
            th, td {{
                padding: 10px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }}
            th {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-weight: bold;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            .chart {{
                text-align: center;
                margin: 20px 0;
            }}
            .chart img {{
                max-width: 100%;
                height: auto;
                border: 1px solid #ddd;
                border-radius: 4px;
            }}
            .chart-title {{
                text-align: center;
                margin-top: 10px;
                font-size: 14px;
                color: {TEXT_SECONDARY};
                font-style: italic;
            }}
        """
    
    def generate_html_section(self, section: Dict, charts: Optional[Dict] = None) -> str:
        """生成HTML章节"""
        section_html = f"""
        <div class="section">
            <div class="section-title">{section.get('title', '')}</div>
        """
        
        section_type = section.get('type', '')
        content = section.get('content', {})
        
        if section_type in ['info', 'summary', 'comparison']:
            section_html += self.generate_html_table(content, ['项目', '值'])
        elif section_type == 'diagnosis':
            if content:
                section_html += self.generate_html_diagnosis_table(content)
        elif section_type == 'file_list':
            if content:
                section_html += self.generate_html_file_list_table(content)
        elif section_type == 'features':
            if content:
                section_html += self.generate_html_features_content(content)
        
        # 添加图表（如果有）
        if charts and section_type in charts:
            chart_data = charts[section_type]
            if chart_data:
                section_html += f"""
                <div class="chart">
                    <img src="data:image/png;base64,{chart_data}" alt="图表">
                    <div class="chart-title">图表: {section.get('title', '')}</div>
                </div>
                """
        
        section_html += "</div>"
        
        return section_html
    
    def generate_html_table(self, content: Dict, headers: List[str]) -> str:
        """生成HTML表格"""
        html = f"""
        <table>
            <thead>
                <tr>
                    {''.join(f'<th>{header}</th>' for header in headers)}
                </tr>
            </thead>
            <tbody>
        """
        
        for key, value in content.items():
            html += f"<tr><td>{key}</td><td>{value}</td></tr>"
        
        html += """
            </tbody>
        </table>
        """
        
        return html
    
    def generate_html_diagnosis_table(self, content: List[Dict]) -> str:
        """生成诊断结果HTML表格"""
        headers = ["诊断时间", "算法类型", "算法名称", "故障类型", "置信度", "状态"]
        
        html = f"""
        <table>
            <thead>
                <tr>
                    {''.join(f'<th>{header}</th>' for header in headers)}
                </tr>
            </thead>
            <tbody>
        """
        
        for result in content:
            html += f"""
            <tr>
                <td>{result.get('诊断时间', '')}</td>
                <td>{result.get('算法类型', '')}</td>
                <td>{result.get('算法名称', '')}</td>
                <td>{result.get('故障类型', '')}</td>
                <td>{result.get('置信度', '')}</td>
                <td>{result.get('状态', '')}</td>
            </tr>
            """
        
        html += """
            </tbody>
        </table>
        """
        
        return html
    
    def generate_html_file_list_table(self, content: List[Dict]) -> str:
        """生成文件列表HTML表格"""
        headers = ["序号", "文件名", "车型", "部件", "测试时间"]
        
        html = f"""
        <table>
            <thead>
                <tr>
                    {''.join(f'<th>{header}</th>' for header in headers)}
                </tr>
            </thead>
            <tbody>
        """
        
        for file_info in content:
            html += f"""
            <tr>
                <td>{file_info.get('序号', '')}</td>
                <td>{file_info.get('文件名', '')}</td>
                <td>{file_info.get('车型', '')}</td>
                <td>{file_info.get('部件', '')}</td>
                <td>{file_info.get('测试时间', '')}</td>
            </tr>
            """
        
        html += """
            </tbody>
        </table>
        """
        
        return html
    
    def generate_html_features_content(self, content: Dict) -> str:
        """生成特征内容HTML"""
        html = ""
        
        for feature_type, features in content.items():
            html += f"<h3>{feature_type}</h3>"
            
            html += """
            <table>
                <thead>
                    <tr>
                        <th>特征名称</th>
                        <th>特征值</th>
                        <th>提取时间</th>
                    </tr>
                </thead>
                <tbody>
            """
            
            for feature in features:
                html += f"""
                <tr>
                    <td>{feature.get('特征名称', '')}</td>
                    <td>{feature.get('特征值', '')}</td>
                    <td>{feature.get('提取时间', '')}</td>
                </tr>
                """
            
            html += """
                </tbody>
            </table>
            """
        
        return html
