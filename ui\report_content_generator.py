"""
报告内容生成模块
负责生成报告的图表、统计分析和诊断结论
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import seaborn as sns
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import base64
from io import BytesIO

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, CHART_COLORS
)
from ui.report_data_model import ReportData, DiagnosisResult, FeatureResult


class ReportContentGenerator:
    """报告内容生成器"""
    
    def __init__(self):
        self.setup_matplotlib()
    
    def setup_matplotlib(self):
        """设置matplotlib样式"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置默认样式
        plt.style.use('default')
        
        # 设置颜色主题
        sns.set_palette(CHART_COLORS)
    
    def generate_diagnosis_summary_chart(self, report_data: ReportData) -> str:
        """生成诊断摘要图表"""
        if not report_data.diagnosis_results:
            return ""
        
        # 统计诊断状态
        status_counts = {'正常': 0, '警告': 0, '故障': 0}
        status_map = {'normal': '正常', 'warning': '警告', 'fault': '故障'}
        
        for result in report_data.diagnosis_results:
            status_text = status_map.get(result.status, result.status)
            status_counts[status_text] = status_counts.get(status_text, 0) + 1
        
        # 创建饼图
        fig, ax = plt.subplots(figsize=(8, 6))
        fig.patch.set_facecolor(PRIMARY_BG)
        
        labels = list(status_counts.keys())
        sizes = list(status_counts.values())
        colors = [SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR]
        
        # 只显示非零的数据
        non_zero_data = [(label, size, color) for label, size, color in zip(labels, sizes, colors) if size > 0]
        if non_zero_data:
            labels, sizes, colors = zip(*non_zero_data)
            
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                            startangle=90, textprops={'fontsize': 12})
            
            # 设置文本颜色
            for text in texts:
                text.set_color(TEXT_PRIMARY)
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
        
        ax.set_title('诊断状态分布', fontsize=16, fontweight='bold', color=TEXT_PRIMARY, pad=20)
        
        # 图表标题放在下方（用户偏好）
        fig.text(0.5, 0.02, '图1: 诊断状态分布统计', ha='center', fontsize=12, color=TEXT_SECONDARY)
        
        plt.tight_layout()
        
        # 转换为base64字符串
        return self.fig_to_base64(fig)
    
    def generate_confidence_trend_chart(self, report_data: ReportData) -> str:
        """生成置信度趋势图表"""
        if not report_data.diagnosis_results:
            return ""
        
        # 按时间排序诊断结果
        sorted_results = sorted(report_data.diagnosis_results, key=lambda x: x.diagnosis_time)
        
        times = [result.diagnosis_time for result in sorted_results]
        confidences = [result.confidence for result in sorted_results]
        
        fig, ax = plt.subplots(figsize=(10, 6))
        fig.patch.set_facecolor(PRIMARY_BG)
        ax.set_facecolor(SECONDARY_BG)
        
        # 绘制置信度趋势线
        ax.plot(times, confidences, marker='o', linewidth=2, markersize=6, 
                color=ACCENT_COLOR, markerfacecolor=HIGHLIGHT_COLOR)
        
        # 添加平均线
        avg_confidence = np.mean(confidences)
        ax.axhline(y=avg_confidence, color=WARNING_COLOR, linestyle='--', 
                  label=f'平均置信度: {avg_confidence:.2%}')
        
        ax.set_xlabel('诊断时间', fontsize=12, color=TEXT_PRIMARY)
        ax.set_ylabel('置信度', fontsize=12, color=TEXT_PRIMARY)
        ax.set_title('诊断置信度趋势', fontsize=16, fontweight='bold', color=TEXT_PRIMARY)
        
        # 设置Y轴为百分比格式
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))
        
        # 设置网格
        ax.grid(True, alpha=0.3, color=TEXT_SECONDARY)
        
        # 设置坐标轴颜色
        ax.tick_params(colors=TEXT_PRIMARY)
        ax.spines['bottom'].set_color(TEXT_PRIMARY)
        ax.spines['left'].set_color(TEXT_PRIMARY)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        # 添加图例
        ax.legend(loc='upper right')
        
        # 图表标题放在下方
        fig.text(0.5, 0.02, '图2: 诊断置信度随时间变化趋势', ha='center', fontsize=12, color=TEXT_SECONDARY)
        
        plt.tight_layout()
        
        return self.fig_to_base64(fig)
    
    def generate_feature_distribution_chart(self, report_data: ReportData) -> str:
        """生成特征分布图表"""
        if not report_data.feature_results:
            return ""
        
        # 按特征类型分组
        feature_groups = {}
        for feature in report_data.feature_results:
            if feature.feature_type not in feature_groups:
                feature_groups[feature.feature_type] = []
            feature_groups[feature.feature_type].append(feature)
        
        # 选择前4个特征类型进行展示
        top_types = list(feature_groups.keys())[:4]
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.patch.set_facecolor(PRIMARY_BG)
        axes = axes.flatten()
        
        for i, feature_type in enumerate(top_types):
            if i >= 4:
                break
                
            features = feature_groups[feature_type]
            feature_names = [f.feature_name for f in features]
            feature_values = [f.feature_value for f in features]
            
            ax = axes[i]
            ax.set_facecolor(SECONDARY_BG)
            
            # 创建柱状图
            bars = ax.bar(range(len(feature_names)), feature_values, 
                         color=CHART_COLORS[i % len(CHART_COLORS)], alpha=0.8)
            
            ax.set_title(feature_type, fontsize=14, fontweight='bold', color=TEXT_PRIMARY)
            ax.set_xlabel('特征名称', fontsize=10, color=TEXT_PRIMARY)
            ax.set_ylabel('特征值', fontsize=10, color=TEXT_PRIMARY)
            
            # 设置X轴标签
            ax.set_xticks(range(len(feature_names)))
            ax.set_xticklabels(feature_names, rotation=45, ha='right', fontsize=8)
            
            # 设置网格
            ax.grid(True, alpha=0.3, color=TEXT_SECONDARY)
            
            # 设置坐标轴颜色
            ax.tick_params(colors=TEXT_PRIMARY)
            ax.spines['bottom'].set_color(TEXT_PRIMARY)
            ax.spines['left'].set_color(TEXT_PRIMARY)
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            
            # 在柱子上显示数值
            for bar, value in zip(bars, feature_values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.2e}' if abs(value) < 0.01 or abs(value) > 1000 else f'{value:.2f}',
                       ha='center', va='bottom', fontsize=8, color=TEXT_PRIMARY)
        
        # 隐藏未使用的子图
        for i in range(len(top_types), 4):
            axes[i].set_visible(False)
        
        plt.suptitle('特征分布分析', fontsize=16, fontweight='bold', color=TEXT_PRIMARY)
        
        # 图表标题放在下方
        fig.text(0.5, 0.02, '图3: 主要特征类型分布情况', ha='center', fontsize=12, color=TEXT_SECONDARY)
        
        plt.tight_layout()
        
        return self.fig_to_base64(fig)
    
    def generate_algorithm_performance_chart(self, report_data: ReportData) -> str:
        """生成算法性能对比图表"""
        if not report_data.diagnosis_results:
            return ""
        
        # 按算法统计性能
        algorithm_stats = {}
        for result in report_data.diagnosis_results:
            algo_name = result.algorithm_name
            if algo_name not in algorithm_stats:
                algorithm_stats[algo_name] = {
                    'count': 0,
                    'total_confidence': 0,
                    'fault_count': 0
                }
            
            algorithm_stats[algo_name]['count'] += 1
            algorithm_stats[algo_name]['total_confidence'] += result.confidence
            if result.status == 'fault':
                algorithm_stats[algo_name]['fault_count'] += 1
        
        # 计算平均置信度和故障检出率
        algorithms = list(algorithm_stats.keys())
        avg_confidences = [stats['total_confidence'] / stats['count'] for stats in algorithm_stats.values()]
        fault_rates = [stats['fault_count'] / stats['count'] for stats in algorithm_stats.values()]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        fig.patch.set_facecolor(PRIMARY_BG)
        
        # 平均置信度图
        ax1.set_facecolor(SECONDARY_BG)
        bars1 = ax1.bar(algorithms, avg_confidences, color=ACCENT_COLOR, alpha=0.8)
        ax1.set_title('算法平均置信度', fontsize=14, fontweight='bold', color=TEXT_PRIMARY)
        ax1.set_xlabel('算法名称', fontsize=12, color=TEXT_PRIMARY)
        ax1.set_ylabel('平均置信度', fontsize=12, color=TEXT_PRIMARY)
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))
        
        # 在柱子上显示数值
        for bar, value in zip(bars1, avg_confidences):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.1%}', ha='center', va='bottom', fontsize=10, color=TEXT_PRIMARY)
        
        # 故障检出率图
        ax2.set_facecolor(SECONDARY_BG)
        bars2 = ax2.bar(algorithms, fault_rates, color=ERROR_COLOR, alpha=0.8)
        ax2.set_title('故障检出率', fontsize=14, fontweight='bold', color=TEXT_PRIMARY)
        ax2.set_xlabel('算法名称', fontsize=12, color=TEXT_PRIMARY)
        ax2.set_ylabel('故障检出率', fontsize=12, color=TEXT_PRIMARY)
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))
        
        # 在柱子上显示数值
        for bar, value in zip(bars2, fault_rates):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.1%}', ha='center', va='bottom', fontsize=10, color=TEXT_PRIMARY)
        
        # 设置样式
        for ax in [ax1, ax2]:
            ax.tick_params(colors=TEXT_PRIMARY)
            ax.grid(True, alpha=0.3, color=TEXT_SECONDARY)
            ax.spines['bottom'].set_color(TEXT_PRIMARY)
            ax.spines['left'].set_color(TEXT_PRIMARY)
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            
            # 旋转X轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        
        # 图表标题放在下方
        fig.text(0.5, 0.02, '图4: 各算法性能对比分析', ha='center', fontsize=12, color=TEXT_SECONDARY)
        
        plt.tight_layout()
        
        return self.fig_to_base64(fig)
    
    def fig_to_base64(self, fig) -> str:
        """将matplotlib图形转换为base64字符串"""
        buffer = BytesIO()
        fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight', 
                   facecolor=PRIMARY_BG, edgecolor='none')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        buffer.close()
        plt.close(fig)
        return image_base64
    
    def generate_diagnostic_conclusions(self, report_data: ReportData) -> Dict:
        """生成诊断结论"""
        conclusions = {
            'overall_status': '未知',
            'risk_level': '低',
            'recommendations': [],
            'summary': ''
        }
        
        if not report_data.diagnosis_results:
            conclusions['summary'] = '暂无诊断数据，无法生成结论。'
            return conclusions
        
        # 统计诊断状态
        status_counts = {'normal': 0, 'warning': 0, 'fault': 0}
        total_confidence = 0
        
        for result in report_data.diagnosis_results:
            status_counts[result.status] = status_counts.get(result.status, 0) + 1
            total_confidence += result.confidence
        
        total_diagnoses = len(report_data.diagnosis_results)
        avg_confidence = total_confidence / total_diagnoses
        
        # 确定整体状态
        fault_rate = status_counts['fault'] / total_diagnoses
        warning_rate = status_counts['warning'] / total_diagnoses
        
        if fault_rate > 0.5:
            conclusions['overall_status'] = '故障'
            conclusions['risk_level'] = '高'
        elif fault_rate > 0.2 or warning_rate > 0.5:
            conclusions['overall_status'] = '警告'
            conclusions['risk_level'] = '中'
        else:
            conclusions['overall_status'] = '正常'
            conclusions['risk_level'] = '低'
        
        # 生成建议
        if conclusions['overall_status'] == '故障':
            conclusions['recommendations'].extend([
                '立即停机检查，避免设备进一步损坏',
                '联系专业技术人员进行详细诊断',
                '检查轴承润滑状况和安装情况',
                '考虑更换相关部件'
            ])
        elif conclusions['overall_status'] == '警告':
            conclusions['recommendations'].extend([
                '加强监测频率，密切关注设备状态',
                '检查润滑系统是否正常',
                '适当降低运行负荷',
                '制定预防性维护计划'
            ])
        else:
            conclusions['recommendations'].extend([
                '继续正常运行，保持定期监测',
                '按计划进行预防性维护',
                '保持良好的润滑和清洁'
            ])
        
        # 生成摘要
        conclusions['summary'] = f"""
        基于 {total_diagnoses} 次诊断分析，设备整体状态为"{conclusions['overall_status']}"，
        风险等级为"{conclusions['risk_level']}"。平均诊断置信度为 {avg_confidence:.1%}。
        其中正常状态 {status_counts['normal']} 次，警告状态 {status_counts['warning']} 次，
        故障状态 {status_counts['fault']} 次。
        """
        
        return conclusions
    
    def generate_statistical_summary(self, report_data: ReportData) -> Dict:
        """生成统计摘要"""
        summary = {
            'file_info': {},
            'diagnosis_stats': {},
            'feature_stats': {},
            'time_range': {}
        }
        
        # 文件信息
        summary['file_info'] = {
            '车型': report_data.file_info.vehicle_type,
            '部件': report_data.file_info.component,
            '传感器类型': report_data.file_info.sensor_type,
            '传感器编号': report_data.file_info.sensor_id,
            '测试时间': report_data.file_info.test_time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 诊断统计
        if report_data.diagnosis_results:
            confidences = [r.confidence for r in report_data.diagnosis_results]
            summary['diagnosis_stats'] = {
                '诊断次数': len(report_data.diagnosis_results),
                '平均置信度': f"{np.mean(confidences):.2%}",
                '最高置信度': f"{np.max(confidences):.2%}",
                '最低置信度': f"{np.min(confidences):.2%}",
                '置信度标准差': f"{np.std(confidences):.2%}"
            }
        
        # 特征统计
        if report_data.feature_results:
            feature_types = set(f.feature_type for f in report_data.feature_results)
            summary['feature_stats'] = {
                '特征总数': len(report_data.feature_results),
                '特征类型数': len(feature_types),
                '特征类型': ', '.join(feature_types)
            }
        
        # 时间范围
        if report_data.diagnosis_results:
            times = [r.diagnosis_time for r in report_data.diagnosis_results]
            summary['time_range'] = {
                '最早诊断': min(times).strftime('%Y-%m-%d %H:%M:%S'),
                '最晚诊断': max(times).strftime('%Y-%m-%d %H:%M:%S'),
                '时间跨度': str(max(times) - min(times))
            }
        
        return summary
