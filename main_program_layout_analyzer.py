"""
主程序环境下的故障异常报警页面布局精确分析器
专门分析主程序中导航栏、分割器等对卡片布局的实际影响
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QWidget, QLabel
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

class MainProgramLayoutAnalyzer:
    """主程序布局分析器"""
    
    def __init__(self):
        self.app = QApplication.instance()
        if not self.app:
            self.app = QApplication(sys.argv)
        
        self.main_window = None
        self.fault_alarm_widget = None
        self.analysis_results = []
    
    def find_main_window(self):
        """查找主程序窗口"""
        for widget in self.app.topLevelWidgets():
            if hasattr(widget, 'windowTitle') and '地面数据分析决策系统' in widget.windowTitle():
                self.main_window = widget
                print(f"找到主程序窗口: {widget.windowTitle()}")
                return True
        return False
    
    def find_fault_alarm_page(self):
        """查找故障异常报警页面"""
        if not self.main_window:
            return False
        
        # 查找工作区
        workspace = None
        for child in self.main_window.findChildren(QWidget):
            if hasattr(child, 'objectName') and 'workspace' in child.objectName().lower():
                workspace = child
                break
            elif hasattr(child, 'currentWidget'):  # QStackedWidget
                workspace = child
                break
        
        if workspace:
            # 查找故障报警系统
            for child in workspace.findChildren(QWidget):
                if child.__class__.__name__ == 'FaultAlarmSystem':
                    self.fault_alarm_widget = child
                    print(f"找到故障异常报警页面: {child.__class__.__name__}")
                    return True
        
        return False
    
    def navigate_to_fault_alarm_page(self):
        """导航到故障异常报警页面"""
        if not self.main_window:
            return False
        
        # 查找导航栏
        nav_bar = None
        for child in self.main_window.findChildren(QWidget):
            if child.__class__.__name__ == 'NavigationBar':
                nav_bar = child
                break
        
        if nav_bar and hasattr(nav_bar, 'nav_buttons'):
            # 点击故障异常报警按钮（索引6）
            if len(nav_bar.nav_buttons) > 6:
                button = nav_bar.nav_buttons[6]
                # 模拟点击
                if hasattr(nav_bar, 'on_nav_clicked'):
                    nav_bar.on_nav_clicked(6)
                    print("已导航到故障异常报警页面")
                    return True
        
        return False
    
    def analyze_main_program_layout(self):
        """分析主程序环境下的布局"""
        print("\n=== 主程序环境布局分析 ===")
        
        if not self.find_main_window():
            print("❌ 未找到主程序窗口")
            return
        
        # 导航到故障异常报警页面
        if not self.navigate_to_fault_alarm_page():
            print("❌ 无法导航到故障异常报警页面")
            return
        
        # 等待页面加载
        time.sleep(1)
        
        if not self.find_fault_alarm_page():
            print("❌ 未找到故障异常报警页面")
            return
        
        # 分析主窗口结构
        self.analyze_main_window_structure()
        
        # 分析工作区尺寸
        self.analyze_workspace_dimensions()
        
        # 分析卡片布局
        self.analyze_cards_layout()
        
        # 生成报告
        self.generate_analysis_report()
    
    def analyze_main_window_structure(self):
        """分析主窗口结构"""
        print("\n--- 主窗口结构分析 ---")
        
        main_rect = self.main_window.geometry()
        print(f"主窗口尺寸: {main_rect.width()} x {main_rect.height()}")
        
        # 查找分割器
        splitter = None
        for child in self.main_window.findChildren(QWidget):
            if child.__class__.__name__ == 'QSplitter':
                splitter = child
                break
        
        if splitter:
            sizes = splitter.sizes()
            print(f"分割器尺寸分配: {sizes}")
            print(f"导航栏实际宽度: {sizes[0]}px")
            print(f"工作区实际宽度: {sizes[1]}px")
        
        # 查找导航栏
        nav_bar = None
        for child in self.main_window.findChildren(QWidget):
            if child.__class__.__name__ == 'NavigationBar':
                nav_bar = child
                break
        
        if nav_bar:
            nav_rect = nav_bar.geometry()
            print(f"导航栏实际尺寸: {nav_rect.width()} x {nav_rect.height()}")
    
    def analyze_workspace_dimensions(self):
        """分析工作区尺寸"""
        print("\n--- 工作区尺寸分析 ---")
        
        # 查找工作区容器
        workspace_container = self.fault_alarm_widget.parent()
        while workspace_container and workspace_container != self.main_window:
            if hasattr(workspace_container, 'geometry'):
                rect = workspace_container.geometry()
                print(f"容器 {workspace_container.__class__.__name__}: {rect.width()} x {rect.height()}")
            workspace_container = workspace_container.parent()
        
        # 分析故障报警系统的实际尺寸
        if self.fault_alarm_widget:
            fault_rect = self.fault_alarm_widget.geometry()
            print(f"故障报警系统尺寸: {fault_rect.width()} x {fault_rect.height()}")
    
    def analyze_cards_layout(self):
        """分析卡片布局"""
        print("\n--- 卡片布局精确分析 ---")
        
        if not self.fault_alarm_widget:
            return
        
        # 查找卡片
        cards = []
        if hasattr(self.fault_alarm_widget, 'fault_card'):
            cards.append(("故障判断卡片", self.fault_alarm_widget.fault_card))
        if hasattr(self.fault_alarm_widget, 'classical_card'):
            cards.append(("传统分类器卡片", self.fault_alarm_widget.classical_card))
        if hasattr(self.fault_alarm_widget, 'dl_card'):
            cards.append(("深度学习卡片", self.fault_alarm_widget.dl_card))
        
        total_asymmetry = 0
        total_items = 0
        
        for card_name, card in cards:
            print(f"\n{card_name}:")
            card_rect = card.geometry()
            print(f"  卡片尺寸: {card_rect.width()} x {card_rect.height()}")
            print(f"  卡片位置: ({card_rect.x()}, {card_rect.y()})")
            
            # 分析卡片内的数据项
            asymmetry_sum = self.analyze_card_data_items(card)
            if asymmetry_sum is not None:
                total_asymmetry += asymmetry_sum[0]
                total_items += asymmetry_sum[1]
        
        # 计算总体对称性
        if total_items > 0:
            avg_asymmetry = total_asymmetry / total_items
            print(f"\n=== 主程序环境对称性评估 ===")
            print(f"平均不对称度: {avg_asymmetry:.1f}px")
            
            if avg_asymmetry <= 1:
                print("✅ 主程序环境布局对称性: 优秀")
            elif avg_asymmetry <= 3:
                print("⚠️ 主程序环境布局对称性: 需要微调")
            else:
                print("❌ 主程序环境布局对称性: 需要重新调整")
                self.suggest_adjustments(avg_asymmetry)
    
    def analyze_card_data_items(self, card):
        """分析卡片内数据项的对称性"""
        if not hasattr(card, 'data_layout'):
            return None
        
        data_layout = card.data_layout
        total_asymmetry = 0
        item_count = 0
        
        for i in range(data_layout.count()):
            item = data_layout.itemAt(i)
            if item and item.layout():
                asymmetry = self.measure_item_symmetry(item.layout(), card, i + 1)
                if asymmetry is not None:
                    total_asymmetry += abs(asymmetry)
                    item_count += 1
        
        return (total_asymmetry, item_count) if item_count > 0 else None
    
    def measure_item_symmetry(self, item_layout, card, item_num):
        """测量单个数据项的对称性"""
        label_widget = None
        value_widget = None
        
        # 查找标签和数值控件
        for i in range(item_layout.count()):
            item = item_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'text') and widget.text().strip():
                    if i == 0:  # 标签
                        label_widget = widget
                    elif i == 2:  # 数值
                        value_widget = widget
        
        if label_widget and value_widget:
            # 计算相对于卡片的位置
            card_width = card.width()
            
            # 获取控件在卡片中的绝对位置
            label_pos = label_widget.mapTo(card, label_widget.rect().topLeft())
            value_pos = value_widget.mapTo(card, value_widget.rect().topLeft())
            
            label_left_margin = label_pos.x()
            value_right_margin = card_width - (value_pos.x() + value_widget.width())
            
            asymmetry = label_left_margin - value_right_margin
            
            print(f"    数据项 {item_num}:")
            print(f"      标签左边距: {label_left_margin}px")
            print(f"      数值右边距: {value_right_margin}px")
            print(f"      不对称度: {asymmetry:+.0f}px")
            
            return asymmetry
        
        return None
    
    def suggest_adjustments(self, avg_asymmetry):
        """建议调整方案"""
        print(f"\n=== 调整建议 ===")
        
        if avg_asymmetry > 5:
            print("建议调整卡片内边距:")
            print("- 减少左内边距 2-3px")
            print("- 增加右内边距 2-3px")
        elif avg_asymmetry > 2:
            print("建议微调数据区域边距:")
            print("- 调整数据区域左右边距差值 1-2px")
        
        print("建议的具体修改:")
        print("layout.setContentsMargins(left-1, 12, right+1, 15)")
        print("或")
        print("self.data_layout.setContentsMargins(left-1, 8, right+1, 8)")
    
    def generate_analysis_report(self):
        """生成分析报告"""
        print(f"\n{'='*50}")
        print("主程序环境布局分析完成")
        print("请根据上述分析结果调整布局参数")
        print(f"{'='*50}")

def main():
    """主函数"""
    print("主程序环境布局分析器")
    print("请确保主程序已经启动...")
    
    analyzer = MainProgramLayoutAnalyzer()
    
    # 等待主程序启动
    time.sleep(2)
    
    # 开始分析
    analyzer.analyze_main_program_layout()

if __name__ == "__main__":
    main()
