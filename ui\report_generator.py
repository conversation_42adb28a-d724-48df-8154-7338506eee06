"""
检测报告生成页面
基于现有功能生成综合检测报告
"""

import os
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QComboBox, QDateEdit, QTextEdit, QScrollArea, QFrame,
    QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QProgressBar, QMessageBox, QFileDialog, QSplitter,
    QCheckBox, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, HIGHLIGHT_COLOR
)
from ui.report_data_model import (
    ReportData, FileInfo, FeatureResult, DiagnosisResult, AnalysisSummary,
    SingleFileReportTemplate, ComparativeReportTemplate, ReportDataValidator
)
from ui.report_content_generator import ReportContentGenerator
from ui.report_exporter import ReportExporter
from database.db_manager import DatabaseManager


class ReportGenerationThread(QThread):
    """报告生成线程"""
    
    progress_updated = pyqtSignal(int, str)
    report_generated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, db_manager, report_config):
        super().__init__()
        self.db_manager = db_manager
        self.report_config = report_config
    
    def run(self):
        """执行报告生成"""
        try:
            self.progress_updated.emit(10, "正在获取文件信息...")
            
            # 获取选中的文件信息
            file_ids = self.report_config.get('file_ids', [])
            if not file_ids:
                self.error_occurred.emit("未选择任何文件")
                return
            
            report_data_list = []
            
            for i, file_id in enumerate(file_ids):
                progress = 10 + (i / len(file_ids)) * 60
                self.progress_updated.emit(int(progress), f"正在处理文件 {i+1}/{len(file_ids)}...")
                
                # 获取文件信息
                file_info_dict = self.db_manager.get_file_by_id(file_id)
                if not file_info_dict:
                    continue
                
                # 转换为FileInfo对象
                file_info = FileInfo(
                    id=file_info_dict['ID'],
                    test_time=file_info_dict['测试时间'],
                    vehicle_type=file_info_dict['车型'],
                    component=file_info_dict['部件'],
                    sensor_type=file_info_dict['传感器类型'],
                    sensor_id=file_info_dict['传感器编号'],
                    file_path=file_info_dict['TDMS文件路径'],
                    file_name=file_info_dict['TDMS文件名'],
                    full_path=file_info_dict['完整路径']
                )
                
                # 获取诊断结果
                diagnosis_results = []
                diagnosis_data = self.db_manager.get_diagnosis_results(
                    file_id=file_id,
                    start_date=self.report_config.get('start_date'),
                    end_date=self.report_config.get('end_date')
                )
                
                for diag in diagnosis_data:
                    diagnosis_result = DiagnosisResult(
                        algorithm_type=diag['algorithm_type'],
                        algorithm_name=diag['algorithm_name'],
                        fault_type=diag['fault_type'],
                        confidence=float(diag['confidence']) if diag['confidence'] else 0.0,
                        status=diag['status'],
                        diagnosis_time=diag['diagnosis_time'],
                        details=json.loads(diag['details']) if diag['details'] else None
                    )
                    diagnosis_results.append(diagnosis_result)
                
                # 获取特征提取结果
                feature_results = []
                feature_data = self.db_manager.get_feature_extraction_results(file_id=file_id)
                
                for feat in feature_data:
                    feature_result = FeatureResult(
                        feature_type=feat['feature_type'],
                        feature_name=feat['feature_name'],
                        feature_value=float(feat['feature_value']) if feat['feature_value'] else 0.0,
                        extraction_time=feat['extraction_time'],
                        parameters=json.loads(feat['parameters']) if feat['parameters'] else None
                    )
                    feature_results.append(feature_result)
                
                # 获取分析摘要
                summary_data = self.db_manager.get_analysis_summary(file_id)
                analysis_summary = None
                if summary_data and summary_data.get('diagnosis_stats'):
                    stats = summary_data['diagnosis_stats']
                    feature_stats = summary_data.get('feature_stats', {})
                    
                    analysis_summary = AnalysisSummary(
                        total_diagnoses=stats.get('total_diagnoses', 0),
                        normal_count=stats.get('normal_count', 0),
                        warning_count=stats.get('warning_count', 0),
                        fault_count=stats.get('fault_count', 0),
                        last_diagnosis=stats.get('last_diagnosis'),
                        avg_confidence=float(stats.get('avg_confidence', 0)) if stats.get('avg_confidence') else 0.0,
                        total_features=feature_stats.get('total_features', 0),
                        feature_types=feature_stats.get('feature_types', 0),
                        last_extraction=feature_stats.get('last_extraction')
                    )
                
                # 创建报告数据
                report_data = ReportData(
                    file_info=file_info,
                    feature_results=feature_results,
                    diagnosis_results=diagnosis_results,
                    analysis_summary=analysis_summary
                )
                
                report_data_list.append(report_data)
            
            self.progress_updated.emit(80, "正在生成报告内容...")

            # 生成报告内容
            report_type = self.report_config.get('report_type', 'single')
            if report_type == 'single' and report_data_list:
                template = SingleFileReportTemplate()
                report_content = template.generate_content(report_data_list[0])

                # 生成图表
                self.progress_updated.emit(90, "正在生成图表...")
                charts = self.generate_charts_for_single_report(report_data_list[0])

            elif report_type == 'comparative':
                template = ComparativeReportTemplate()
                report_content = template.generate_content(report_data_list)

                # 生成对比图表
                self.progress_updated.emit(90, "正在生成对比图表...")
                charts = self.generate_charts_for_comparative_report(report_data_list)

            else:
                self.error_occurred.emit("无效的报告类型或无数据")
                return

            # 将图表添加到报告内容
            result = {
                'report_content': report_content,
                'charts': charts
            }

            self.progress_updated.emit(100, "报告生成完成")
            self.report_generated.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(f"报告生成失败: {str(e)}")

    def generate_charts_for_single_report(self, report_data: ReportData) -> Dict:
        """为单文件报告生成图表"""
        from ui.report_content_generator import ReportContentGenerator

        charts = {}
        content_generator = ReportContentGenerator()

        try:
            # 生成诊断摘要图表
            if report_data.diagnosis_results:
                charts['diagnosis_summary'] = content_generator.generate_diagnosis_summary_chart(report_data)
                charts['confidence_trend'] = content_generator.generate_confidence_trend_chart(report_data)
                charts['algorithm_performance'] = content_generator.generate_algorithm_performance_chart(report_data)

            # 生成特征分布图表
            if report_data.feature_results:
                charts['feature_distribution'] = content_generator.generate_feature_distribution_chart(report_data)

        except Exception as e:
            print(f"图表生成失败: {e}")

        return charts

    def generate_charts_for_comparative_report(self, report_data_list: List[ReportData]) -> Dict:
        """为对比报告生成图表"""
        charts = {}

        try:
            # 这里可以添加对比图表的生成逻辑
            # 例如：多文件诊断结果对比、特征对比等
            pass
        except Exception as e:
            print(f"对比图表生成失败: {e}")

        return charts


class ReportGenerator(QWidget):
    """检测报告生成页面"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.current_report = None
        self.current_charts = None
        self.content_generator = ReportContentGenerator()
        self.exporter = ReportExporter()
        self.init_ui()
        self.load_available_files()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📋 检测报告生成")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
        """)
        title_label.setMaximumHeight(40)
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧配置面板
        config_panel = self.create_config_panel()
        config_panel.setMaximumWidth(350)
        splitter.addWidget(config_panel)
        
        # 右侧预览面板
        preview_panel = self.create_preview_panel()
        splitter.addWidget(preview_panel)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 0)  # 配置面板固定宽度
        splitter.setStretchFactor(1, 1)  # 预览面板可伸缩
        
        layout.addWidget(splitter)
    
    def create_config_panel(self) -> QWidget:
        """创建配置面板"""
        panel = QFrame()
        panel.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border: 1px solid #cccccc;
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # 报告类型选择
        type_group = QGroupBox("报告类型")
        type_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 16px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                font-family: 'Microsoft YaHei';
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        
        type_layout = QVBoxLayout(type_group)
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems(["单文件分析报告", "多文件对比报告"])
        self.report_type_combo.setStyleSheet(f"""
            QComboBox {{
                font-size: 14px;
                padding: 8px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
                font-family: 'Microsoft YaHei';
            }}
        """)
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        type_layout.addWidget(self.report_type_combo)
        
        layout.addWidget(type_group)
        
        # 文件选择
        file_group = QGroupBox("文件选择")
        file_group.setStyleSheet(type_group.styleSheet())
        
        file_layout = QVBoxLayout(file_group)
        
        self.file_list = QListWidget()
        self.file_list.setStyleSheet(f"""
            QListWidget {{
                font-size: 12px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
                font-family: 'Microsoft YaHei';
            }}
            QListWidget::item {{
                padding: 5px;
                border-bottom: 1px solid #eeeeee;
            }}
            QListWidget::item:selected {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
        """)
        self.file_list.setSelectionMode(QListWidget.MultiSelection)
        file_layout.addWidget(self.file_list)
        
        layout.addWidget(file_group)
        
        # 日期范围选择
        date_group = QGroupBox("日期范围")
        date_group.setStyleSheet(type_group.styleSheet())
        
        date_layout = QVBoxLayout(date_group)
        
        # 开始日期
        start_layout = QHBoxLayout()
        start_layout.addWidget(QLabel("开始日期:"))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setStyleSheet(f"""
            QDateEdit {{
                font-size: 12px;
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
                font-family: 'Microsoft YaHei';
            }}
        """)
        start_layout.addWidget(self.start_date)
        date_layout.addLayout(start_layout)
        
        # 结束日期
        end_layout = QHBoxLayout()
        end_layout.addWidget(QLabel("结束日期:"))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setStyleSheet(self.start_date.styleSheet())
        end_layout.addWidget(self.end_date)
        date_layout.addLayout(end_layout)
        
        layout.addWidget(date_group)
        
        # 生成按钮
        self.generate_btn = QPushButton("🔄 生成报告")
        self.generate_btn.clicked.connect(self.generate_report)
        self.generate_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
                border-radius: 8px;
                border: none;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """)
        layout.addWidget(self.generate_btn)
        
        # 导出按钮
        export_layout = QHBoxLayout()
        
        self.export_pdf_btn = QPushButton("📄 导出PDF")
        self.export_pdf_btn.clicked.connect(self.export_pdf)
        self.export_pdf_btn.setEnabled(False)
        self.export_pdf_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {SUCCESS_COLOR};
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
                border-radius: 6px;
                border: none;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #00c9a7;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """)
        export_layout.addWidget(self.export_pdf_btn)
        
        self.export_html_btn = QPushButton("🌐 导出HTML")
        self.export_html_btn.clicked.connect(self.export_html)
        self.export_html_btn.setEnabled(False)
        self.export_html_btn.setStyleSheet(self.export_pdf_btn.styleSheet())
        export_layout.addWidget(self.export_html_btn)
        
        layout.addLayout(export_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #cccccc;
                border-radius: 4px;
                text-align: center;
                font-family: 'Microsoft YaHei';
            }}
            QProgressBar::chunk {{
                background-color: {ACCENT_COLOR};
                border-radius: 3px;
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("请选择文件并配置报告参数")
        self.status_label.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 12px;
            font-family: 'Microsoft YaHei';
            padding: 5px;
        """)
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        return panel

    def create_preview_panel(self) -> QWidget:
        """创建预览面板"""
        panel = QFrame()
        panel.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #cccccc;
                border-radius: 8px;
            }}
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)

        # 预览标题
        preview_title = QLabel("📊 报告预览")
        preview_title.setStyleSheet(f"""
            font-size: 18px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
        """)
        layout.addWidget(preview_title)

        # 预览内容区域
        self.preview_area = QScrollArea()
        self.preview_area.setWidgetResizable(True)
        self.preview_area.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid #eeeeee;
                border-radius: 4px;
                background-color: white;
            }}
        """)

        # 默认预览内容
        default_content = QLabel("请配置报告参数并点击"生成报告"按钮")
        default_content.setAlignment(Qt.AlignCenter)
        default_content.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 16px;
            font-family: 'Microsoft YaHei';
            padding: 50px;
        """)
        self.preview_area.setWidget(default_content)

        layout.addWidget(self.preview_area)

        return panel

    def load_available_files(self):
        """加载可用文件列表"""
        try:
            files = self.db_manager.get_tdms_files()
            self.file_list.clear()

            for file_info in files:
                item_text = f"{file_info['车型']} - {file_info['部件']} - {file_info['TDMS文件名']}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, file_info['ID'])  # 存储文件ID
                self.file_list.addItem(item)

            self.status_label.setText(f"已加载 {len(files)} 个文件")

        except Exception as e:
            self.status_label.setText(f"加载文件列表失败: {str(e)}")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 12px;")

    def on_report_type_changed(self, report_type: str):
        """报告类型改变事件"""
        if report_type == "单文件分析报告":
            self.file_list.setSelectionMode(QListWidget.SingleSelection)
            self.status_label.setText("单文件模式：请选择一个文件")
        else:
            self.file_list.setSelectionMode(QListWidget.MultiSelection)
            self.status_label.setText("对比模式：请选择多个文件")

    def generate_report(self):
        """生成报告"""
        # 获取选中的文件
        selected_items = self.file_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请至少选择一个文件")
            return

        # 获取文件ID列表
        file_ids = [item.data(Qt.UserRole) for item in selected_items]

        # 获取报告类型
        report_type = 'single' if self.report_type_combo.currentText() == "单文件分析报告" else 'comparative'

        # 验证选择
        if report_type == 'single' and len(file_ids) > 1:
            QMessageBox.warning(self, "警告", "单文件分析报告只能选择一个文件")
            return

        if report_type == 'comparative' and len(file_ids) < 2:
            QMessageBox.warning(self, "警告", "对比分析报告至少需要选择两个文件")
            return

        # 配置报告参数
        report_config = {
            'file_ids': file_ids,
            'report_type': report_type,
            'start_date': self.start_date.date().toString('yyyy-MM-dd'),
            'end_date': self.end_date.date().toString('yyyy-MM-dd')
        }

        # 开始生成报告
        self.start_report_generation(report_config)

    def start_report_generation(self, report_config: Dict):
        """开始报告生成"""
        self.generate_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在生成报告...")

        # 创建并启动生成线程
        self.generation_thread = ReportGenerationThread(self.db_manager, report_config)
        self.generation_thread.progress_updated.connect(self.on_progress_updated)
        self.generation_thread.report_generated.connect(self.on_report_generated)
        self.generation_thread.error_occurred.connect(self.on_generation_error)
        self.generation_thread.start()

    def on_progress_updated(self, progress: int, message: str):
        """进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_report_generated(self, result: Dict):
        """报告生成完成"""
        self.current_report = result.get('report_content', {})
        self.current_charts = result.get('charts', {})
        self.display_report_preview(self.current_report)

        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.export_pdf_btn.setEnabled(True)
        self.export_html_btn.setEnabled(True)
        self.status_label.setText("报告生成完成")
        self.status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 12px;")

    def on_generation_error(self, error_message: str):
        """报告生成错误"""
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"生成失败: {error_message}")
        self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 12px;")

        QMessageBox.critical(self, "错误", f"报告生成失败:\n{error_message}")

    def display_report_preview(self, report_content: Dict):
        """显示报告预览"""
        preview_widget = QWidget()
        layout = QVBoxLayout(preview_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # 报告标题
        title_label = QLabel(report_content.get('title', '检测报告'))
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            text-align: center;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 副标题
        if report_content.get('subtitle'):
            subtitle_label = QLabel(report_content['subtitle'])
            subtitle_label.setStyleSheet(f"""
                font-size: 16px;
                color: {TEXT_SECONDARY};
                text-align: center;
                margin-bottom: 5px;
                font-family: 'Microsoft YaHei';
            """)
            subtitle_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(subtitle_label)

        # 生成时间
        time_label = QLabel(f"生成时间: {report_content.get('generated_time', '')}")
        time_label.setStyleSheet(f"""
            font-size: 12px;
            color: {TEXT_SECONDARY};
            text-align: center;
            margin-bottom: 20px;
            font-family: 'Microsoft YaHei';
        """)
        time_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(time_label)

        # 报告章节
        for section in report_content.get('sections', []):
            section_widget = self.create_section_widget(section)
            layout.addWidget(section_widget)

        layout.addStretch()

        self.preview_area.setWidget(preview_widget)

    def create_section_widget(self, section: Dict) -> QWidget:
        """创建章节组件"""
        section_frame = QFrame()
        section_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border: 1px solid #dddddd;
                border-radius: 8px;
                margin: 5px 0;
                padding: 15px;
            }}
        """)

        layout = QVBoxLayout(section_frame)

        # 章节标题
        title_label = QLabel(section.get('title', ''))
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
        """)
        layout.addWidget(title_label)

        # 章节内容
        section_type = section.get('type', '')
        content = section.get('content', {})

        if section_type == 'info':
            content_widget = self.create_info_content(content)
        elif section_type == 'summary':
            content_widget = self.create_summary_content(content)
        elif section_type == 'diagnosis':
            content_widget = self.create_diagnosis_content(content)
        elif section_type == 'features':
            content_widget = self.create_features_content(content)
        elif section_type == 'file_list':
            content_widget = self.create_file_list_content(content)
        elif section_type == 'comparison':
            content_widget = self.create_comparison_content(content)
        else:
            content_widget = QLabel("未知章节类型")

        layout.addWidget(content_widget)

        return section_frame

    def create_info_content(self, content: Dict) -> QWidget:
        """创建信息内容组件"""
        table = QTableWidget()
        table.setColumnCount(2)
        table.setHorizontalHeaderLabels(["项目", "值"])
        table.setRowCount(len(content))

        for i, (key, value) in enumerate(content.items()):
            table.setItem(i, 0, QTableWidgetItem(str(key)))
            table.setItem(i, 1, QTableWidgetItem(str(value)))

        # 设置表格样式
        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: white;
                border: 1px solid #dddddd;
                border-radius: 4px;
                font-family: 'Microsoft YaHei';
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #eeeeee;
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
            }}
        """)

        # 调整列宽
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        table.setColumnWidth(0, 150)

        # 隐藏行号
        table.verticalHeader().setVisible(False)

        return table

    def create_summary_content(self, content: Dict) -> QWidget:
        """创建摘要内容组件"""
        return self.create_info_content(content)

    def create_diagnosis_content(self, content: List[Dict]) -> QWidget:
        """创建诊断内容组件"""
        if not content:
            return QLabel("暂无诊断结果")

        table = QTableWidget()
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels(["诊断时间", "算法类型", "算法名称", "故障类型", "置信度", "状态"])
        table.setRowCount(len(content))

        for i, result in enumerate(content):
            table.setItem(i, 0, QTableWidgetItem(result.get('诊断时间', '')))
            table.setItem(i, 1, QTableWidgetItem(result.get('算法类型', '')))
            table.setItem(i, 2, QTableWidgetItem(result.get('算法名称', '')))
            table.setItem(i, 3, QTableWidgetItem(result.get('故障类型', '')))
            table.setItem(i, 4, QTableWidgetItem(result.get('置信度', '')))
            table.setItem(i, 5, QTableWidgetItem(result.get('状态', '')))

        # 设置表格样式
        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: white;
                border: 1px solid #dddddd;
                border-radius: 4px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }}
            QTableWidget::item {{
                padding: 6px;
                border-bottom: 1px solid #eeeeee;
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-weight: bold;
                padding: 6px;
                border: none;
            }}
        """)

        # 调整列宽
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        table.verticalHeader().setVisible(False)

        return table

    def create_features_content(self, content: Dict) -> QWidget:
        """创建特征内容组件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        for feature_type, features in content.items():
            # 特征类型标题
            type_label = QLabel(feature_type)
            type_label.setStyleSheet(f"""
                font-size: 16px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin: 10px 0 5px 0;
                font-family: 'Microsoft YaHei';
            """)
            layout.addWidget(type_label)

            # 特征表格
            table = QTableWidget()
            table.setColumnCount(3)
            table.setHorizontalHeaderLabels(["特征名称", "特征值", "提取时间"])
            table.setRowCount(len(features))

            for i, feature in enumerate(features):
                table.setItem(i, 0, QTableWidgetItem(feature.get('特征名称', '')))
                table.setItem(i, 1, QTableWidgetItem(str(feature.get('特征值', ''))))
                table.setItem(i, 2, QTableWidgetItem(feature.get('提取时间', '')))

            table.setStyleSheet(f"""
                QTableWidget {{
                    background-color: white;
                    border: 1px solid #dddddd;
                    border-radius: 4px;
                    font-family: 'Microsoft YaHei';
                    font-size: 12px;
                }}
                QTableWidget::item {{
                    padding: 5px;
                    border-bottom: 1px solid #eeeeee;
                }}
                QHeaderView::section {{
                    background-color: {SECONDARY_BG};
                    color: {TEXT_PRIMARY};
                    font-weight: bold;
                    padding: 5px;
                    border: none;
                }}
            """)

            table.horizontalHeader().setStretchLastSection(True)
            table.verticalHeader().setVisible(False)
            table.setMaximumHeight(200)

            layout.addWidget(table)

        return widget

    def create_file_list_content(self, content: List[Dict]) -> QWidget:
        """创建文件列表内容组件"""
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["序号", "文件名", "车型", "部件", "测试时间"])
        table.setRowCount(len(content))

        for i, file_info in enumerate(content):
            table.setItem(i, 0, QTableWidgetItem(str(file_info.get('序号', ''))))
            table.setItem(i, 1, QTableWidgetItem(file_info.get('文件名', '')))
            table.setItem(i, 2, QTableWidgetItem(file_info.get('车型', '')))
            table.setItem(i, 3, QTableWidgetItem(file_info.get('部件', '')))
            table.setItem(i, 4, QTableWidgetItem(file_info.get('测试时间', '')))

        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: white;
                border: 1px solid #dddddd;
                border-radius: 4px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }}
            QTableWidget::item {{
                padding: 6px;
                border-bottom: 1px solid #eeeeee;
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-weight: bold;
                padding: 6px;
                border: none;
            }}
        """)

        table.horizontalHeader().setStretchLastSection(True)
        table.verticalHeader().setVisible(False)

        return table

    def create_comparison_content(self, content: Dict) -> QWidget:
        """创建对比内容组件"""
        return self.create_info_content(content)

    def export_pdf(self):
        """导出PDF"""
        if not self.current_report:
            QMessageBox.warning(self, "警告", "请先生成报告")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存PDF报告",
            f"检测报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            "PDF文件 (*.pdf)"
        )

        if file_path:
            try:
                self.save_pdf_report(file_path)
                QMessageBox.information(self, "成功", f"PDF报告已保存到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"PDF导出失败:\n{str(e)}")

    def export_html(self):
        """导出HTML"""
        if not self.current_report:
            QMessageBox.warning(self, "警告", "请先生成报告")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存HTML报告",
            f"检测报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
            "HTML文件 (*.html)"
        )

        if file_path:
            try:
                self.save_html_report(file_path)
                QMessageBox.information(self, "成功", f"HTML报告已保存到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"HTML导出失败:\n{str(e)}")

    def save_pdf_report(self, file_path: str):
        """保存PDF报告"""
        try:
            self.exporter.export_to_pdf(self.current_report, file_path, self.current_charts)
        except ImportError as e:
            raise ImportError("PDF导出需要安装reportlab库。请运行: pip install reportlab")
        except Exception as e:
            raise Exception(f"PDF导出失败: {str(e)}")

    def save_html_report(self, file_path: str):
        """保存HTML报告"""
        try:
            self.exporter.export_to_html(self.current_report, file_path, self.current_charts)
        except Exception as e:
            raise Exception(f"HTML导出失败: {str(e)}")

    def generate_html_content(self, report_content: Dict) -> str:
        """生成HTML内容"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{report_content.get('title', '检测报告')}</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid {ACCENT_COLOR};
                    padding-bottom: 20px;
                }}
                .title {{
                    font-size: 28px;
                    font-weight: bold;
                    color: {ACCENT_COLOR};
                    margin-bottom: 10px;
                }}
                .subtitle {{
                    font-size: 16px;
                    color: {TEXT_SECONDARY};
                    margin-bottom: 5px;
                }}
                .generated-time {{
                    font-size: 12px;
                    color: {TEXT_SECONDARY};
                }}
                .section {{
                    margin-bottom: 30px;
                    padding: 20px;
                    background-color: {SECONDARY_BG};
                    border-radius: 8px;
                    border-left: 4px solid {ACCENT_COLOR};
                }}
                .section-title {{
                    font-size: 20px;
                    font-weight: bold;
                    color: {ACCENT_COLOR};
                    margin-bottom: 15px;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 10px;
                }}
                th, td {{
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                th {{
                    background-color: {ACCENT_COLOR};
                    color: white;
                    font-weight: bold;
                }}
                tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
                .chart-title {{
                    text-align: center;
                    margin-top: 10px;
                    font-size: 14px;
                    color: {TEXT_SECONDARY};
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="title">{report_content.get('title', '检测报告')}</div>
                    <div class="subtitle">{report_content.get('subtitle', '')}</div>
                    <div class="generated-time">生成时间: {report_content.get('generated_time', '')}</div>
                </div>
        """

        # 添加各个章节
        for section in report_content.get('sections', []):
            html += self.generate_html_section(section)

        html += """
            </div>
        </body>
        </html>
        """

        return html

    def generate_html_section(self, section: Dict) -> str:
        """生成HTML章节"""
        section_html = f"""
        <div class="section">
            <div class="section-title">{section.get('title', '')}</div>
        """

        section_type = section.get('type', '')
        content = section.get('content', {})

        if section_type in ['info', 'summary', 'comparison']:
            section_html += self.generate_html_table(content, ['项目', '值'])
        elif section_type == 'diagnosis':
            if content:
                section_html += self.generate_html_diagnosis_table(content)
        elif section_type == 'file_list':
            if content:
                section_html += self.generate_html_file_list_table(content)

        section_html += "</div>"

        return section_html

    def generate_html_table(self, content: Dict, headers: List[str]) -> str:
        """生成HTML表格"""
        html = f"""
        <table>
            <thead>
                <tr>
                    {''.join(f'<th>{header}</th>' for header in headers)}
                </tr>
            </thead>
            <tbody>
        """

        for key, value in content.items():
            html += f"<tr><td>{key}</td><td>{value}</td></tr>"

        html += """
            </tbody>
        </table>
        """

        return html

    def generate_html_diagnosis_table(self, content: List[Dict]) -> str:
        """生成诊断结果HTML表格"""
        headers = ["诊断时间", "算法类型", "算法名称", "故障类型", "置信度", "状态"]

        html = f"""
        <table>
            <thead>
                <tr>
                    {''.join(f'<th>{header}</th>' for header in headers)}
                </tr>
            </thead>
            <tbody>
        """

        for result in content:
            html += f"""
            <tr>
                <td>{result.get('诊断时间', '')}</td>
                <td>{result.get('算法类型', '')}</td>
                <td>{result.get('算法名称', '')}</td>
                <td>{result.get('故障类型', '')}</td>
                <td>{result.get('置信度', '')}</td>
                <td>{result.get('状态', '')}</td>
            </tr>
            """

        html += """
            </tbody>
        </table>
        """

        return html

    def generate_html_file_list_table(self, content: List[Dict]) -> str:
        """生成文件列表HTML表格"""
        headers = ["序号", "文件名", "车型", "部件", "测试时间"]

        html = f"""
        <table>
            <thead>
                <tr>
                    {''.join(f'<th>{header}</th>' for header in headers)}
                </tr>
            </thead>
            <tbody>
        """

        for file_info in content:
            html += f"""
            <tr>
                <td>{file_info.get('序号', '')}</td>
                <td>{file_info.get('文件名', '')}</td>
                <td>{file_info.get('车型', '')}</td>
                <td>{file_info.get('部件', '')}</td>
                <td>{file_info.get('测试时间', '')}</td>
            </tr>
            """

        html += """
            </tbody>
        </table>
        """

        return html
