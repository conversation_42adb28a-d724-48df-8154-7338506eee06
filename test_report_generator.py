#!/usr/bin/env python3
"""
检测报告生成功能测试脚本
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

from database.db_manager import DatabaseManager
from ui.report_generator import ReportGenerator
from ui.report_data_model import (
    ReportData, FileInfo, FeatureResult, DiagnosisResult, AnalysisSummary,
    SingleFileReportTemplate, ReportDataValidator
)


def test_report_data_model():
    """测试报告数据模型"""
    print("测试报告数据模型...")
    
    # 创建测试数据
    file_info = FileInfo(
        id=1,
        test_time=datetime.now(),
        vehicle_type="测试车型",
        component="轴承",
        sensor_type="振动传感器",
        sensor_id="SENSOR001",
        file_path="D:/test/",
        file_name="test_file",
        full_path="D:/test/test_file.tdms"
    )
    
    feature_results = [
        FeatureResult(
            feature_type="时域特征",
            feature_name="均值",
            feature_value=0.123,
            extraction_time=datetime.now()
        ),
        FeatureResult(
            feature_type="时域特征",
            feature_name="标准差",
            feature_value=0.456,
            extraction_time=datetime.now()
        )
    ]
    
    diagnosis_results = [
        DiagnosisResult(
            algorithm_type="经典算法",
            algorithm_name="马氏距离",
            fault_type="外圈故障",
            confidence=0.85,
            status="fault",
            diagnosis_time=datetime.now()
        )
    ]
    
    analysis_summary = AnalysisSummary(
        total_diagnoses=1,
        normal_count=0,
        warning_count=0,
        fault_count=1,
        avg_confidence=0.85,
        total_features=2,
        feature_types=1
    )
    
    report_data = ReportData(
        file_info=file_info,
        feature_results=feature_results,
        diagnosis_results=diagnosis_results,
        analysis_summary=analysis_summary
    )
    
    # 验证数据
    validator = ReportDataValidator()
    errors = validator.validate_report_data(report_data)
    
    if errors:
        print(f"数据验证失败: {errors}")
        return False
    else:
        print("数据验证通过")
    
    # 测试模板
    template = SingleFileReportTemplate()
    content = template.generate_content(report_data)
    
    print(f"报告标题: {content.get('title')}")
    print(f"章节数量: {len(content.get('sections', []))}")
    
    return True


def test_database_integration():
    """测试数据库集成"""
    print("测试数据库集成...")
    
    try:
        db_manager = DatabaseManager()
        success, message = db_manager.test_connection()
        
        if success:
            print("数据库连接成功")
            
            # 测试获取文件列表
            files = db_manager.get_tdms_files()
            print(f"获取到 {len(files)} 个TDMS文件")
            
            if files:
                # 测试获取诊断结果
                file_id = files[0]['ID']
                diagnosis_results = db_manager.get_diagnosis_results(file_id=file_id)
                print(f"文件 {file_id} 有 {len(diagnosis_results)} 个诊断结果")
                
                # 测试获取特征提取结果
                feature_results = db_manager.get_feature_extraction_results(file_id=file_id)
                print(f"文件 {file_id} 有 {len(feature_results)} 个特征提取结果")
            
            return True
        else:
            print(f"数据库连接失败: {message}")
            return False
            
    except Exception as e:
        print(f"数据库测试失败: {e}")
        return False


def test_report_generator_ui():
    """测试报告生成器UI"""
    print("测试报告生成器UI...")
    
    try:
        app = QApplication(sys.argv)
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 创建报告生成器
        report_generator = ReportGenerator(db_manager)
        report_generator.show()
        
        print("报告生成器UI创建成功")
        print("请手动测试UI功能...")
        
        # 运行应用
        # app.exec_()  # 注释掉以避免阻塞测试
        
        return True
        
    except Exception as e:
        print(f"UI测试失败: {e}")
        return False


def test_export_functionality():
    """测试导出功能"""
    print("测试导出功能...")
    
    try:
        from ui.report_exporter import ReportExporter
        
        exporter = ReportExporter()
        
        # 创建测试报告内容
        test_report = {
            'title': '测试报告',
            'subtitle': '测试副标题',
            'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'sections': [
                {
                    'type': 'info',
                    'title': '基本信息',
                    'content': {
                        '车型': '测试车型',
                        '部件': '轴承',
                        '传感器': 'SENSOR001'
                    }
                }
            ]
        }
        
        # 测试HTML导出
        html_file = "test_report.html"
        success = exporter.export_to_html(test_report, html_file)
        
        if success and os.path.exists(html_file):
            print(f"HTML导出成功: {html_file}")
            os.remove(html_file)  # 清理测试文件
        else:
            print("HTML导出失败")
            return False
        
        # 测试PDF导出（可能因为缺少reportlab而失败）
        try:
            pdf_file = "test_report.pdf"
            success = exporter.export_to_pdf(test_report, pdf_file)
            
            if success and os.path.exists(pdf_file):
                print(f"PDF导出成功: {pdf_file}")
                os.remove(pdf_file)  # 清理测试文件
            else:
                print("PDF导出失败")
        except ImportError:
            print("PDF导出跳过（需要安装reportlab库）")
        
        return True
        
    except Exception as e:
        print(f"导出功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("检测报告生成功能测试")
    print("=" * 60)
    
    tests = [
        ("报告数据模型", test_report_data_model),
        ("数据库集成", test_database_integration),
        ("导出功能", test_export_functionality),
        ("报告生成器UI", test_report_generator_ui),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"{test_name}: 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！报告生成功能已就绪。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")


if __name__ == "__main__":
    main()
