"""
报告数据模型模块
定义报告生成所需的数据结构和模板
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from datetime import datetime
import json


@dataclass
class FileInfo:
    """TDMS文件信息"""
    id: int
    test_time: datetime
    vehicle_type: str
    component: str
    sensor_type: str
    sensor_id: str
    file_path: str
    file_name: str
    full_path: str


@dataclass
class FeatureResult:
    """特征提取结果"""
    feature_type: str
    feature_name: str
    feature_value: float
    extraction_time: datetime
    parameters: Optional[Dict] = None


@dataclass
class DiagnosisResult:
    """诊断结果"""
    algorithm_type: str
    algorithm_name: str
    fault_type: str
    confidence: float
    status: str  # 'normal', 'warning', 'fault'
    diagnosis_time: datetime
    details: Optional[Dict] = None


@dataclass
class AnalysisSummary:
    """分析摘要"""
    total_diagnoses: int = 0
    normal_count: int = 0
    warning_count: int = 0
    fault_count: int = 0
    last_diagnosis: Optional[datetime] = None
    avg_confidence: float = 0.0
    total_features: int = 0
    feature_types: int = 0
    last_extraction: Optional[datetime] = None


@dataclass
class ReportData:
    """报告数据容器"""
    file_info: FileInfo
    feature_results: List[FeatureResult] = field(default_factory=list)
    diagnosis_results: List[DiagnosisResult] = field(default_factory=list)
    analysis_summary: Optional[AnalysisSummary] = None
    charts: List[Dict] = field(default_factory=list)  # 图表数据
    generated_time: datetime = field(default_factory=datetime.now)


class ReportTemplate:
    """报告模板基类"""
    
    def __init__(self, template_name: str):
        self.template_name = template_name
        self.sections = []
    
    def add_section(self, section_type: str, title: str, content: Any):
        """添加报告章节"""
        self.sections.append({
            'type': section_type,
            'title': title,
            'content': content
        })
    
    def generate_content(self, report_data: ReportData) -> Dict:
        """生成报告内容"""
        raise NotImplementedError("子类必须实现此方法")


class SingleFileReportTemplate(ReportTemplate):
    """单文件分析报告模板"""
    
    def __init__(self):
        super().__init__("单文件分析报告")
    
    def generate_content(self, report_data: ReportData) -> Dict:
        """生成单文件分析报告内容"""
        content = {
            'title': f"{report_data.file_info.vehicle_type} - {report_data.file_info.component} 故障诊断报告",
            'subtitle': f"文件: {report_data.file_info.file_name}",
            'generated_time': report_data.generated_time.strftime("%Y-%m-%d %H:%M:%S"),
            'sections': []
        }
        
        # 1. 文件基本信息
        file_section = {
            'type': 'info',
            'title': '文件基本信息',
            'content': {
                '测试时间': report_data.file_info.test_time.strftime("%Y-%m-%d %H:%M:%S"),
                '车型': report_data.file_info.vehicle_type,
                '部件': report_data.file_info.component,
                '传感器类型': report_data.file_info.sensor_type,
                '传感器编号': report_data.file_info.sensor_id,
                '文件路径': report_data.file_info.full_path
            }
        }
        content['sections'].append(file_section)
        
        # 2. 分析摘要
        if report_data.analysis_summary:
            summary_section = {
                'type': 'summary',
                'title': '分析摘要',
                'content': {
                    '诊断次数': report_data.analysis_summary.total_diagnoses,
                    '正常状态': report_data.analysis_summary.normal_count,
                    '警告状态': report_data.analysis_summary.warning_count,
                    '故障状态': report_data.analysis_summary.fault_count,
                    '平均置信度': f"{report_data.analysis_summary.avg_confidence:.2%}" if report_data.analysis_summary.avg_confidence else "N/A",
                    '特征提取数量': report_data.analysis_summary.total_features,
                    '特征类型数量': report_data.analysis_summary.feature_types
                }
            }
            content['sections'].append(summary_section)
        
        # 3. 诊断结果
        if report_data.diagnosis_results:
            diagnosis_section = {
                'type': 'diagnosis',
                'title': '诊断结果',
                'content': []
            }
            
            for result in report_data.diagnosis_results:
                diagnosis_item = {
                    '诊断时间': result.diagnosis_time.strftime("%Y-%m-%d %H:%M:%S"),
                    '算法类型': result.algorithm_type,
                    '算法名称': result.algorithm_name,
                    '故障类型': result.fault_type,
                    '置信度': f"{result.confidence:.2%}",
                    '状态': self._get_status_text(result.status),
                    '详细信息': result.details
                }
                diagnosis_section['content'].append(diagnosis_item)
            
            content['sections'].append(diagnosis_section)
        
        # 4. 特征提取结果
        if report_data.feature_results:
            feature_section = {
                'type': 'features',
                'title': '特征提取结果',
                'content': self._group_features_by_type(report_data.feature_results)
            }
            content['sections'].append(feature_section)
        
        # 5. 图表分析
        if report_data.charts:
            chart_section = {
                'type': 'charts',
                'title': '图表分析',
                'content': report_data.charts
            }
            content['sections'].append(chart_section)
        
        return content
    
    def _get_status_text(self, status: str) -> str:
        """获取状态文本"""
        status_map = {
            'normal': '正常',
            'warning': '警告',
            'fault': '故障'
        }
        return status_map.get(status, status)
    
    def _group_features_by_type(self, features: List[FeatureResult]) -> Dict:
        """按类型分组特征"""
        grouped = {}
        for feature in features:
            if feature.feature_type not in grouped:
                grouped[feature.feature_type] = []
            
            grouped[feature.feature_type].append({
                '特征名称': feature.feature_name,
                '特征值': feature.feature_value,
                '提取时间': feature.extraction_time.strftime("%Y-%m-%d %H:%M:%S")
            })
        
        return grouped


class ComparativeReportTemplate(ReportTemplate):
    """对比分析报告模板"""
    
    def __init__(self):
        super().__init__("对比分析报告")
    
    def generate_content(self, report_data_list: List[ReportData]) -> Dict:
        """生成对比分析报告内容"""
        content = {
            'title': '多文件对比分析报告',
            'subtitle': f'共分析 {len(report_data_list)} 个文件',
            'generated_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'sections': []
        }
        
        # 1. 文件列表
        file_list_section = {
            'type': 'file_list',
            'title': '分析文件列表',
            'content': []
        }
        
        for i, data in enumerate(report_data_list, 1):
            file_info = {
                '序号': i,
                '文件名': data.file_info.file_name,
                '车型': data.file_info.vehicle_type,
                '部件': data.file_info.component,
                '测试时间': data.file_info.test_time.strftime("%Y-%m-%d %H:%M:%S")
            }
            file_list_section['content'].append(file_info)
        
        content['sections'].append(file_list_section)
        
        # 2. 对比摘要
        comparison_section = {
            'type': 'comparison',
            'title': '对比分析摘要',
            'content': self._generate_comparison_summary(report_data_list)
        }
        content['sections'].append(comparison_section)
        
        return content
    
    def _generate_comparison_summary(self, report_data_list: List[ReportData]) -> Dict:
        """生成对比摘要"""
        total_files = len(report_data_list)
        total_diagnoses = sum(len(data.diagnosis_results) for data in report_data_list)
        
        status_counts = {'normal': 0, 'warning': 0, 'fault': 0}
        for data in report_data_list:
            for result in data.diagnosis_results:
                status_counts[result.status] = status_counts.get(result.status, 0) + 1
        
        return {
            '分析文件数': total_files,
            '总诊断次数': total_diagnoses,
            '正常状态数': status_counts['normal'],
            '警告状态数': status_counts['warning'],
            '故障状态数': status_counts['fault'],
            '故障率': f"{(status_counts['fault'] / total_diagnoses * 100):.1f}%" if total_diagnoses > 0 else "0%"
        }


class ReportDataValidator:
    """报告数据验证器"""
    
    @staticmethod
    def validate_report_data(report_data: ReportData) -> List[str]:
        """验证报告数据"""
        errors = []
        
        # 验证文件信息
        if not report_data.file_info:
            errors.append("缺少文件信息")
        elif not report_data.file_info.file_name:
            errors.append("文件名不能为空")
        
        # 验证诊断结果
        for i, result in enumerate(report_data.diagnosis_results):
            if not result.algorithm_name:
                errors.append(f"诊断结果 {i+1}: 算法名称不能为空")
            if result.confidence < 0 or result.confidence > 1:
                errors.append(f"诊断结果 {i+1}: 置信度必须在0-1之间")
            if result.status not in ['normal', 'warning', 'fault']:
                errors.append(f"诊断结果 {i+1}: 无效的状态值")
        
        # 验证特征结果
        for i, feature in enumerate(report_data.feature_results):
            if not feature.feature_name:
                errors.append(f"特征结果 {i+1}: 特征名称不能为空")
            if not feature.feature_type:
                errors.append(f"特征结果 {i+1}: 特征类型不能为空")
        
        return errors


class ReportDataFormatter:
    """报告数据格式化器"""
    
    @staticmethod
    def format_confidence(confidence: float) -> str:
        """格式化置信度"""
        return f"{confidence:.2%}"
    
    @staticmethod
    def format_datetime(dt: datetime) -> str:
        """格式化日期时间"""
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def format_feature_value(value: float, precision: int = 6) -> str:
        """格式化特征值"""
        return f"{value:.{precision}f}"
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
